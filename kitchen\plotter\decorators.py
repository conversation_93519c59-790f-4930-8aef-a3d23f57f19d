from collections import namedtuple
from typing import Callable, Optional, <PERSON>ple
import matplotlib.pyplot as plt
import numpy as np
from functools import wraps
import os

from kitchen.configs import routing
from kitchen.plotter.style_dicts import REFERENCE_LINE_STYLE
from kitchen.structure.hierarchical_data_structure import DataSet, Node


def default_style_single_ax(
        tight_layout: bool = True,    
        invisible_spines: bool = True, 
        xticks_flag: bool = True,
        yticks_flag: bool = False,   
        title: str = "", 
        xlabel: str = "", 
        ylabel: str = ""):
    """
    A decorator to default style a Matplotlib plot with a single axis.

    Args:
        tight_layout (bool): Whether to apply tight layout.
        invisible_spines (bool): Whether to make top and right spines invisible.
        title (str): The title of the plot.
        xlabel (str): The label for the x-axis.
        ylabel (str): The label for the y-axis.
    """
    plt.rcParams["font.family"] = "Arial"
    plt.rcParams['font.size'] = 8
    plt.rcParams.update({
        'xtick.labelsize': 5,      # X-axis tick labels
        'ytick.labelsize': 5,      # Y-axis tick labels
        'axes.labelsize': 6,       # X and Y axis labels
        'legend.fontsize': 6,      # Legend font size
        'axes.titlesize': 7,       # Plot title
        'figure.titlesize': 8      # Figure title (suptitle)
    })

    def decorator(plot_func: Callable[..., Tuple[float, float]]):
        @wraps(plot_func)
        def wrapper(dataset: DataSet | Node, save_name: Optional[str] = None, *args, **kwargs):
            # Create figure and axis
            fig, ax = plt.subplots(1, 1)

            # Set axis labels and title
            ax.set_title(title)
            ax.set_xlabel(xlabel)
            ax.set_ylabel(ylabel)
            if not xticks_flag:
                ax.set_xticks([])
            if not yticks_flag:
                ax.set_yticks([])

            # Call plotting function
            dataset = dataset if isinstance(dataset, DataSet) else DataSet([dataset])
            figsize = plot_func(ax, dataset, *args, **kwargs)
            fig.set_size_inches(*figsize)

            # Apply default styles
            if invisible_spines:
                ax.spines[['top', 'right']].set_visible(False)
            if tight_layout:
                fig.tight_layout()

            # Save or show the figure
            if save_name:
                save_path = routing.robust_path_join(routing.default_fig_path(dataset), save_name)
                fig.savefig(save_path, dpi=600)
                print(f"Plot saved to {save_path}")
            else:
                plt.show()
            
            # Close the figure
            plt.close(fig)
        return wrapper
    return decorator


BBOX = namedtuple("BBOX", ["ymin", "ymax"])

class TraceStacker:
    """
    A decorator that provides vertical offset to plotting functions, 
    allowing them to stack traces vertically.
    """
    def __init__(self, ax: plt.Axes, padding_factor: float = 0.1, base_padding: float = 1):
        self.ax = ax
        self.padding_factor = padding_factor
        self.base_padding = base_padding
        self.offset: float = 0.0

    def __call__(self, plot_func: Callable[..., BBOX]):
        """The wrapping logic"""        
        @wraps(plot_func)
        def wrapper(*args, **kwargs):       
            bbox = plot_func(*args, ax=self.ax, y_offset=self.offset, **kwargs)          
            bbox_height = (bbox.ymax - bbox.ymin) * (1 + self.padding_factor) + self.base_padding
            self.offset += bbox_height
        return wrapper
