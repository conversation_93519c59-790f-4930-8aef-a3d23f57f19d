import matplotlib.pyplot as plt
import numpy as np

from kitchen.settings.fluorescence import DF_F0_SIGN
from kitchen.plotter.color_scheme import FLUORESCENCE_COLOR, LOCOMOTION_COLOR, POSITION_COLOR, LICK_COLOR, <PERSON><PERSON><PERSON>_COLOR, WHISKE<PERSON>_COLOR
from kitchen.plotter.decorators import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, default_style_single_ax
from kitchen.plotter.plotting_params import FLUORESCENCE_RATIO, LICK_BIN_SIZE, LICK_RATIO, LOCOMOTION_BIN_SIZE, LOCOMOTION_RATIO, POSITION_RATIO, PUPIL_BOUNDARY_PERCENTILE, PUPIL_RATIO, TIMELINE_RATIO, WHISKER_RATIO
from kitchen.plotter.style_dicts import FLUORESCENCE_TRACE_STYLE, LOCOMOTION_TRACE_STYLE, POSITION_SCATTER_STYLE, LICK_VLINES_STYLE, <PERSON><PERSON><PERSON>_TRACE_STYLE, TIMELINE_SCATTER_STYLE, WH<PERSON><PERSON><PERSON>_TRACE_STYLE
from kitchen.plotter.utils.tick_labels import TICK_PAIR, add_new_yticks
from kitchen.structure.hierarchical_data_structure import CellSession, DataSet, Node, Session
from kitchen.structure.neural_data_structure import Events, Fluorescence, NeuralData, TimeSeries
from kitchen.utils.sequence_kit import find_only_one
from kitchen.plotter.data_viewer.unit_view import unit_plot_locomotion, unit_plot_lick, unit_plot_position, unit_plot_pupil, unit_plot_single_cell_fluorescence, unit_plot_timeline, unit_plot_whisker


@default_style_single_ax()
def node_flat_view(
    ax_grand, dataset: DataSet,
    locomotion_flag: bool = True,
    lick_flag: bool = True,
    pupil_flag: bool = True,
    tongue_flag: bool = True,
    whisker_flag: bool = True,
    fluorescence_flag: bool = True,
):
    """Plot a flat view of a node."""
    offset_tracker = TraceStacker(ax_grand)
    node = find_only_one(dataset.nodes)
    
    """plot behavior data from node"""
    # 1. Locomotion
    @offset_tracker
    def plot_locomotion(ax: plt.Axes, y_offset: float, ratio: float = 1.0):
        assert node.data.locomotion is not None, f"Cannot find locomotion in {node}"
        assert isinstance(node.data.locomotion, Events), f"Expected Events, got {type(node.data.locomotion)}"
        return unit_plot_locomotion(node.data.locomotion, ax, y_offset, ratio)
    
    @offset_tracker
    def plot_position(ax: plt.Axes, y_offset: float, ratio: float = 1.0):
        assert node.data.position is not None, f"Cannot find position in {node}"
        assert isinstance(node.data.position, Events), f"Expected Events, got {type(node.data.position)}"
        return unit_plot_position(node.data.position, ax, y_offset, ratio)

    # 2. lick
    @offset_tracker
    def plot_lick(ax: plt.Axes, y_offset: float, ratio: float = 1.0):
        assert node.data.lick is not None, f"Cannot find lick in {node}"
        assert isinstance(node.data.lick, Events), f"Expected Events, got {type(node.data.lick)}"
        return unit_plot_lick(node.data.lick, ax, y_offset, ratio)
    
    # 3. pupil
    @offset_tracker
    def plot_pupil(ax: plt.Axes, y_offset: float, ratio: float = 1.0):
        assert node.data.pupil is not None, f"Cannot find pupil in {node}"
        assert isinstance(node.data.pupil, TimeSeries), f"Expected TimeSeries, got {type(node.data.pupil)}"
        return unit_plot_pupil(node.data.pupil, ax, y_offset, ratio)
    
    # 4. whisker
    @offset_tracker
    def plot_whisker(ax: plt.Axes, y_offset: float, ratio: float = 1.0):
        assert node.data.whisker is not None, f"Cannot find whisker in {node}"
        assert isinstance(node.data.whisker, TimeSeries), f"Expected TimeSeries, got {type(node.data.whisker)}"
        return unit_plot_whisker(node.data.whisker, ax, y_offset, ratio)
    
    """plot timeline from node"""
    # 5. timeline
    @offset_tracker
    def plot_timeline(ax: plt.Axes, y_offset: float, ratio: float = 1.0):
        assert node.data.timeline is not None, f"Cannot find timeline in {node}"
        assert isinstance(node.data.timeline, Events), f"Expected Events, got {type(node.data.timeline)}"
        return unit_plot_timeline(node.data.timeline, ax, y_offset, ratio)

    """plot fluorescence from node"""
    # 6. fluorescence
    def plot_fluorescence(ratio: float = 1.0):
        assert node.data.fluorescence is not None, f"Cannot find fluorescence in {node}"
        assert isinstance(node.data.fluorescence, Fluorescence), f"Expected Fluorescence, got {type(node.data.fluorescence)}"

        plotting_fluorescence = node.data.fluorescence

        @offset_tracker
        def plot_single_cell(ax: plt.Axes, y_offset: float, cell_id: int):
            """plot a single cell"""
            return unit_plot_single_cell_fluorescence(plotting_fluorescence, ax, y_offset, cell_id, ratio)
        
        for cell_id in range(plotting_fluorescence.num_cell):
            plot_single_cell(cell_id=cell_id)

    plot_timeline(ratio=TIMELINE_RATIO)
    
    if fluorescence_flag:
        plot_fluorescence(ratio=FLUORESCENCE_RATIO)

    if lick_flag:
        plot_lick(ratio=LICK_RATIO)

    if locomotion_flag:
        plot_locomotion(ratio=LOCOMOTION_RATIO)        
        plot_position(ratio=POSITION_RATIO)

    if whisker_flag:
        plot_whisker(ratio=WHISKER_RATIO)

    if pupil_flag:
        plot_pupil(ratio=PUPIL_RATIO)

    return 5, offset_tracker.offset / 10