from kitchen.settings.timeline import SUPPORTED_TIMELINE_EVENT
from kitchen.plotter.color_scheme import FLUORESCENCE_COLOR, LOCOMOTION_COLOR, POSITION_COLOR, LICK_COLOR, PUPIL_COLOR, WHISKER_COLOR, <PERSON>U<PERSON>_COLOR, BLANK_COLOR, WATER_COLOR, NOWATER_COLOR, BUZZER_COLOR


REFERENCE_LINE_STYLE = {
    "color": "gray",
    "linewidth": 0.5,
    "linestyle": "--",
    "alpha": 0.7,
}

LOCOMOTION_TRACE_STYLE = {
    "color": LOCOMOTION_COLOR,
    "linewidth": 0.5,
    "alpha": 0.7,
}

POSITION_SCATTER_STYLE = {
    "color": POSITION_COLOR,
    "s": 1,
    "alpha": 0.7,
}

LICK_VLINES_STYLE = {
    "colors": LICK_COLOR,
    "ls": 'solid',
    "alpha": 0.7,
    "lw": 0.1,
}

LICK_TRACE_STYLE = {
    "color": <PERSON><PERSON><PERSON>_COLOR,
    "linewidth": 0.5,
    "alpha": 0.7,
}

PUPIL_TRACE_STYLE = {
    "color": PUPIL_COLOR,
    "linewidth": 0.5,
    "alpha": 0.7,
}

WHISKER_TRACE_STYLE = {
    "color": WHISKER_COLOR,
    "linewidth": 0.5,
    "alpha": 0.7,
}

FLUORESCENCE_TRACE_STYLE = {
    "color": FLUORESCENCE_COLOR,
    "linewidth": 0.5,
    "alpha": 0.7,
}

TIMELINE_SCATTER_STYLE = {
    "VerticalPuffOn": {
        "color": PUFF_COLOR,
        "s": 15,
        "marker": "|",
        "alpha": 1,
        "lw": 1,
    },
    "Puff": {
        "color": PUFF_COLOR,
        "s": 15,
        "marker": "|",
        "alpha": 1,
        "lw": 1,
    },
    "HorizontalPuffOn": {
        "color": PUFF_COLOR,
        "s": 5,
        "marker": "-",
        "alpha": 1,
        "lw": 2,
    },
    "BlankOn": {
        "facecolors": 'none',
        "edgecolors": BLANK_COLOR,
        "s": 8,
        "marker": "o",
        "alpha": 1,
        "lw": 0.5,
    },
    "Blank": {
        "facecolors": 'none',
        "edgecolors": BLANK_COLOR,
        "s": 8,
        "marker": "o",
        "alpha": 1,
        "lw": 0.5,
    },
    "WaterOn": {
        "color": WATER_COLOR,
        "s": 5,
        "marker": "^",
        "alpha": 1,
        "lw": 1.5,
    },
    "NoWaterOn": {
        "color": NOWATER_COLOR,
        "s": 5,
        "marker": "x",
        "alpha": 1,
        "lw": 1.5,
    },
    "BuzzerOn": {
        "color": BUZZER_COLOR,
        "s": 10,
        "marker": "s",
        "alpha": 1,
        "lw": 0,
    },

}

for event_type in TIMELINE_SCATTER_STYLE:
    assert event_type in SUPPORTED_TIMELINE_EVENT, f"Unsupported event {event_type} in TIMELINE_SCATTER_STYLE: {SUPPORTED_TIMELINE_EVENT}"
