import sys
import os
import os.path as path
import warnings

from kitchen.plotter.data_viewer.flat_view import node_flat_view
from kitchen.structure.hierarchical_data_structure import Session
import kitchen.video.format_converter as format_converter
import kitchen.video.custom_extraction as custom_extraction
import kitchen.video.facemap_pupil_extraction as facemap_pupil_extraction
import kitchen.loader.hierarchical_loader as hier_loader

warnings.filterwarnings("ignore")

def pre_conversion():
    hft_data_path = r"E:\Max_Behavior_Training"
    format_converter.video_convert(hft_data_path)
    
def whisker_extraction():
    data_set = hier_loader.naive_loader(template_id="ThreeStage_WaterOmission", cohort_id="HeadFixedTraining_202507")      
    custom_extraction.default_collection(data_set)

def pupil_extraction():
    data_set = hier_loader.naive_loader(template_id="ThreeStage_WaterOmission", cohort_id="HeadFixedTraining_202507")      
    facemap_pupil_extraction.default_collection(data_set)

def main():
    dataset = hier_loader.cohort_loader(template_id="ThreeStage_WaterOmission", cohort_id="HeadFixedTraining_202507") 
    dataset.status(save_path=path.join(path.dirname(__file__), "status_report.xlsx"))
    for session_node in dataset.select("session"):
        node_flat_view(session_node, fluorescence_flag=False)


if __name__ == "__main__":
    # pre_conversion()
    # whisker_extraction()
    # pupil_extraction()
    main()


