import matplotlib.pyplot as plt
import numpy as np

from kitchen.settings.fluorescence import DF_F0_SIGN
from kitchen.plotter.color_scheme import FLUORESCENCE_COLOR, LOCOMOTION_COLOR, POSITION_COLOR, LICK_COLOR, <PERSON><PERSON><PERSON>_COLOR, WHISKER_COLOR
from kitchen.plotter.decorators import BBOX
from kitchen.plotter.plotting_params import LOCOMOTION_BIN_SIZE, PUPIL_BOUNDARY_PERCENTILE
from kitchen.plotter.style_dicts import FLUORESCENCE_TRACE_STYLE, LOCOMOTION_TRACE_STYLE, POSITION_SCATTER_STYLE, LICK_VLINES_STYLE, PUPIL_TRACE_STYLE, TIMELINE_SCATTER_STYLE, WHISKER_TRACE_STYLE
from kitchen.plotter.utils.tick_labels import TICK_PAIR, add_new_yticks
from kitchen.structure.neural_data_structure import Events, Fluorescence, TimeSeries


def unit_plot_locomotion(locomotion: Events, ax: plt.Axes, y_offset: float, ratio: float = 1.0) -> BBOX:
    # plot locomotion rate
    plotting_locomotion = locomotion.rate(bin_size=LOCOMOTION_BIN_SIZE)
    bbox = BBOX(plotting_locomotion.v.min() * ratio, plotting_locomotion.v.max() * ratio)        
    zero_y = y_offset - bbox.ymin
    ax.plot(plotting_locomotion.t, plotting_locomotion.v * ratio + zero_y, **LOCOMOTION_TRACE_STYLE)

    # add y ticks
    add_new_yticks(ax, [TICK_PAIR(zero_y, "Locomotion", LOCOMOTION_COLOR), 
                        TICK_PAIR(zero_y + 2 * ratio, "2 cm/s", LOCOMOTION_COLOR)])
    return bbox


def unit_plot_position(position: Events, ax: plt.Axes, y_offset: float, ratio: float = 1.0) -> BBOX:
    # plot position
    bbox = BBOX(0, ratio)        
    ax.scatter(position.t, position.v * ratio + y_offset, **POSITION_SCATTER_STYLE)        

    # add y ticks
    add_new_yticks(ax, [TICK_PAIR(y_offset, "0°", POSITION_COLOR), 
                        TICK_PAIR(y_offset + 0.5 * ratio, "180°", POSITION_COLOR),
                        TICK_PAIR(y_offset + 1 * ratio, "360°", POSITION_COLOR)])
    return bbox


def unit_plot_lick(lick: Events, ax: plt.Axes, y_offset: float, ratio: float = 1.0) -> BBOX:
    # plot lick
    bbox = BBOX(0, ratio)        
    ax.vlines(x=lick.t, ymin=y_offset, ymax=y_offset + ratio, **LICK_VLINES_STYLE)
    
    # add y ticks
    add_new_yticks(ax, TICK_PAIR(y_offset + 0.5 * ratio, "Lick", LICK_COLOR), add_ref_lines=False)
    return bbox


def unit_plot_pupil(pupil: TimeSeries, ax: plt.Axes, y_offset: float, ratio: float = 1.0) -> BBOX:
    # plot pupil
    down_pupil, up_pupil = np.percentile(pupil.v, PUPIL_BOUNDARY_PERCENTILE)
    bbox = BBOX(down_pupil * ratio, up_pupil * ratio)        
    zero_y = y_offset - bbox.ymin
    ax.plot(pupil.t, pupil.v * ratio + zero_y, **PUPIL_TRACE_STYLE)

    # add y ticks
    add_new_yticks(ax, [TICK_PAIR(zero_y + down_pupil * ratio, f"{PUPIL_BOUNDARY_PERCENTILE[0]}% Pupil", PUPIL_COLOR),
                        TICK_PAIR(zero_y + up_pupil * ratio, f"{PUPIL_BOUNDARY_PERCENTILE[1]}% Pupil", PUPIL_COLOR)])
    return bbox


def unit_plot_whisker(whisker: TimeSeries, ax: plt.Axes, y_offset: float, ratio: float = 1.0) -> BBOX:
    # plot whisker
    bbox = BBOX(0, min(whisker.v.max(), 2) * ratio)        
    ax.plot(whisker.t, whisker.v * ratio + y_offset, **WHISKER_TRACE_STYLE)        

    # add y ticks
    add_new_yticks(ax, [TICK_PAIR(y_offset, "Whisker", WHISKER_COLOR),
                        TICK_PAIR(y_offset + 2 * ratio, "2 A.U.", WHISKER_COLOR)])
    return bbox


def unit_plot_timeline(timeline: Events, ax: plt.Axes, y_offset: float, ratio: float = 1.0) -> BBOX:
    # plot timeline
    bbox = BBOX(0, ratio)        
    for event_time, event_type in zip(timeline.t, timeline.v):
        if event_type not in TIMELINE_SCATTER_STYLE:
            continue
        ax.scatter(event_time, y_offset + 0.5 * ratio , **TIMELINE_SCATTER_STYLE[event_type])

    return bbox


def unit_plot_single_cell_fluorescence(fluorescence: Fluorescence, ax: plt.Axes, y_offset: float, cell_id: int, ratio: float = 1.0) -> BBOX:
    """plot a single cell"""
    cell_trace = fluorescence.detrend_f.v[cell_id]
    bbox = BBOX(cell_trace.min() * ratio, cell_trace.max() * ratio)
    zero_y = y_offset - bbox.ymin
    ax.plot(fluorescence.detrend_f.t, cell_trace * ratio + zero_y, **FLUORESCENCE_TRACE_STYLE)
    add_new_yticks(ax, TICK_PAIR(zero_y, f"Cell {fluorescence.cell_idx[cell_id]}", FLUORESCENCE_COLOR))          
    if cell_id == 0:
        add_new_yticks(ax, TICK_PAIR(zero_y + 1 * ratio, f"1 {DF_F0_SIGN}", FLUORESCENCE_COLOR))  
    return bbox
    
