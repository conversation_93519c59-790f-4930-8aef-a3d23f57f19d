import os
import os.path as path

from kitchen.structure.hierarchical_data_structure import DataSet, Node

ROOT_PATH = (r"C:\Users\<USER>\PycharmProjects\Ratatouille")
DATA_PATH = path.join(ROOT_PATH, "ingredients")
FIGURE_PATH = path.join(ROOT_PATH, "cuisine")
TEST_PATH = path.join(ROOT_PATH, "critic")


def robust_path_join(*args):
    """Safely join path components, filtering out None values."""
    assert len([arg for arg in args if arg is not None]) > 0, \
         f"at least one argument is required, got {args}"
    return path.join(*[str(arg) for arg in args if arg is not None])


def default_data_path(node: Node, check_exist: bool = True) -> str:
    """Generate the default file system path for a node's data."""
    node_obj_uid = node.coordinate.object_uid
    node_data_path = robust_path_join(DATA_PATH, *[value for name, value in node_obj_uid if value is not None])
    if check_exist:
        assert path.exists(node_data_path), f"Cannot find data path: {node_data_path}"
    return node_data_path


def default_fig_path(dataset: DataSet) -> str:
    """Generate the default file system path for a dataset's figures."""
    root_coordinate = dataset.root_coordinate
    fig_path_values = [value for name, value in root_coordinate.object_uid if value is not None]
    if root_coordinate.temporal_uid.session_id is not None:
        fig_path_values.append(root_coordinate.temporal_uid.session_id)
    if root_coordinate.temporal_uid.chunk_id is not None:
        fig_path_values.append(root_coordinate.temporal_uid.chunk_id)    
    fig_path = robust_path_join(FIGURE_PATH, *fig_path_values)
    os.makedirs(fig_path, exist_ok=True)
    return fig_path
