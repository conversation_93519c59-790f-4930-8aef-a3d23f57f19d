import pandas as pd
import numpy as np

# 1. Create the initial DataFrame with True/False values
index_labels = ['Test_A', 'Test_B', 'Test_C', 'Test_D', 'Test_E']
data = np.random.choice([True, False], size=(5, 3))
df = pd.DataFrame(data, index=index_labels, columns=['Feature 1', 'Feature 2', 'Feature 3'])

# 2. Create the final display DataFrame by replacing booleans with symbols
df_display = df.replace({True: '✅', False: '❌'})

# 3. Define a new styling function that sets color based on the symbol
def set_color(symbol):
    if symbol == '✅':
        return 'color: green'
    elif symbol == '❌':
        return 'color: red'
    return ''

# 4. Apply the correct styling function and export
output_filename = 'report_color_corrected.xlsx'
(df_display.style
 .applymap(set_color)
 .to_excel(output_filename, engine='openpyxl', index=True))

print(f"Successfully created '{output_filename}'. The colors are now correct.")